{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\n\nconst Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const menuItems = [\n    { label: 'WORK', href: '#work' },\n    { label: 'ABOUT', href: '#about' },\n    { label: 'CONTACT', href: '#contact' },\n  ];\n\n  return (\n    <motion.nav\n      className=\"fixed top-0 left-0 right-0 z-40 bg-black/90 backdrop-blur-sm border-b border-white/10\"\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 py-4 md:py-6\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-lg md:text-xl font-black tracking-tight\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ duration: 0.2 }}\n          >\n            MOTION\n          </motion.div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {menuItems.map((item, index) => (\n              <motion.a\n                key={item.label}\n                href={item.href}\n                className=\"text-sm font-medium tracking-wide hover:text-white/60 transition-colors\"\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -2 }}\n              >\n                {item.label}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden w-8 h-8 flex flex-col justify-center items-center space-y-1\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            <motion.span\n              className=\"w-6 h-0.5 bg-white block\"\n              animate={{\n                rotate: isMenuOpen ? 45 : 0,\n                y: isMenuOpen ? 6 : 0,\n              }}\n              transition={{ duration: 0.3 }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-white block\"\n              animate={{\n                opacity: isMenuOpen ? 0 : 1,\n              }}\n              transition={{ duration: 0.3 }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-white block\"\n              animate={{\n                rotate: isMenuOpen ? -45 : 0,\n                y: isMenuOpen ? -6 : 0,\n              }}\n              transition={{ duration: 0.3 }}\n            />\n          </motion.button>\n        </div>\n\n        {/* Mobile Menu */}\n        <motion.div\n          className=\"md:hidden overflow-hidden\"\n          initial={{ height: 0 }}\n          animate={{ height: isMenuOpen ? 'auto' : 0 }}\n          transition={{ duration: 0.3, ease: [0.22, 1, 0.36, 1] }}\n        >\n          <div className=\"pt-6 pb-4 space-y-4\">\n            {menuItems.map((item, index) => (\n              <motion.a\n                key={item.label}\n                href={item.href}\n                className=\"block text-lg font-medium tracking-wide hover:text-white/60 transition-colors\"\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ \n                  opacity: isMenuOpen ? 1 : 0, \n                  x: isMenuOpen ? 0 : -20 \n                }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.label}\n              </motion.a>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,YAAY;QAChB;YAAE,OAAO;YAAQ,MAAM;QAAQ;QAC/B;YAAE,OAAO;YAAS,MAAM;QAAS;QACjC;YAAE,OAAO;YAAW,MAAM;QAAW;KACtC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;QAAC;kBAEtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;sCAC7B;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,YAAY;wCAAE,GAAG,CAAC;oCAAE;8CAEnB,KAAK,KAAK;mCARN,KAAK,KAAK;;;;;;;;;;sCAcrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,KAAK;wCAC1B,GAAG,aAAa,IAAI;oCACtB;oCACA,YAAY;wCAAE,UAAU;oCAAI;;;;;;8CAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,SAAS,aAAa,IAAI;oCAC5B;oCACA,YAAY;wCAAE,UAAU;oCAAI;;;;;;8CAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,CAAC,KAAK;wCAC3B,GAAG,aAAa,CAAC,IAAI;oCACvB;oCACA,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;;8BAMlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAE;oBACrB,SAAS;wBAAE,QAAQ,aAAa,SAAS;oBAAE;oBAC3C,YAAY;wBAAE,UAAU;wBAAK,MAAM;4BAAC;4BAAM;4BAAG;4BAAM;yBAAE;oBAAC;8BAEtD,cAAA,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCACP,SAAS,aAAa,IAAI;oCAC1B,GAAG,aAAa,IAAI,CAAC;gCACvB;gCACA,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BAXN,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAmB/B;uCAEe", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/MagneticButton.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useRef, useState, MouseEvent, ReactNode } from 'react';\n\ninterface MagneticButtonProps {\n  children: ReactNode;\n  className?: string;\n  strength?: number;\n  onClick?: () => void;\n}\n\nconst MagneticButton = ({ \n  children, \n  className = '', \n  strength = 0.3,\n  onClick \n}: MagneticButtonProps) => {\n  const ref = useRef<HTMLButtonElement>(null);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n\n  const handleMouseMove = (e: MouseEvent<HTMLButtonElement>) => {\n    if (!ref.current) return;\n\n    const { clientX, clientY } = e;\n    const { left, top, width, height } = ref.current.getBoundingClientRect();\n    \n    const x = (clientX - (left + width / 2)) * strength;\n    const y = (clientY - (top + height / 2)) * strength;\n    \n    setPosition({ x, y });\n  };\n\n  const handleMouseLeave = () => {\n    setPosition({ x: 0, y: 0 });\n  };\n\n  return (\n    <motion.button\n      ref={ref}\n      className={`relative overflow-hidden ${className}`}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      onClick={onClick}\n      animate={{ x: position.x, y: position.y }}\n      transition={{\n        type: \"spring\",\n        stiffness: 150,\n        damping: 15,\n        mass: 0.1,\n      }}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <motion.div\n        className=\"relative z-10\"\n        animate={{ x: position.x * 0.5, y: position.y * 0.5 }}\n        transition={{\n          type: \"spring\",\n          stiffness: 200,\n          damping: 20,\n        }}\n      >\n        {children}\n      </motion.div>\n    </motion.button>\n  );\n};\n\nexport default MagneticButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,iBAAiB,CAAC,EACtB,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,GAAG,EACd,OAAO,EACa;IACpB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,OAAO,EAAE;QAElB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,qBAAqB;QAEtE,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI;QAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,SAAS,CAAC,CAAC,IAAI;QAE3C,YAAY;YAAE;YAAG;QAAE;IACrB;IAEA,MAAM,mBAAmB;QACvB,YAAY;YAAE,GAAG;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAC,yBAAyB,EAAE,WAAW;QAClD,aAAa;QACb,cAAc;QACd,SAAS;QACT,SAAS;YAAE,GAAG,SAAS,CAAC;YAAE,GAAG,SAAS,CAAC;QAAC;QACxC,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;QACA,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;kBAExB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,GAAG,SAAS,CAAC,GAAG;gBAAK,GAAG,SAAS,CAAC,GAAG;YAAI;YACpD,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;sBAEC;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\nimport MagneticButton from './MagneticButton';\n\nconst Hero = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    window.addEventListener('mousemove', updateMousePosition);\n    return () => window.removeEventListener('mousemove', updateMousePosition);\n  }, []);\n\n  const titleVariants = {\n    hidden: { opacity: 0, y: 100 },\n    visible: (i: number) => ({\n      opacity: 1,\n      y: 0,\n      transition: {\n        delay: i * 0.1,\n        duration: 0.8,\n        ease: [0.22, 1, 0.36, 1],\n      },\n    }),\n  };\n\n  const subtitleVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        delay: 0.8,\n        duration: 0.6,\n        ease: [0.22, 1, 0.36, 1],\n      },\n    },\n  };\n\n  const titleWords = ['MOTION', 'GRAPHIC', 'DESIGNER'];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden pt-20\">\n      {/* Background Grid */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\" />\n      </div>\n\n      <div className=\"relative z-10 text-center px-4 max-w-6xl mx-auto\">\n        {/* Main Title */}\n        <div className=\"mb-8\">\n          {titleWords.map((word, index) => (\n            <motion.h1\n              key={word}\n              className=\"text-4xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black text-brutalist leading-[0.8] tracking-tighter block mb-2\"\n              custom={index}\n              initial=\"hidden\"\n              animate=\"visible\"\n              variants={titleVariants}\n              whileHover={{\n                scale: 1.02,\n                transition: { duration: 0.3 }\n              }}\n            >\n              {word}\n            </motion.h1>\n          ))}\n        </div>\n\n        {/* Subtitle */}\n        <motion.div\n          className=\"max-w-2xl mx-auto\"\n          initial=\"hidden\"\n          animate=\"visible\"\n          variants={subtitleVariants}\n        >\n          <p className=\"text-base md:text-lg lg:text-xl text-white/70 font-medium tracking-wide mb-8 leading-relaxed\">\n            CREATING VISUAL NARRATIVES THROUGH\n            <br />\n            <span className=\"text-white\">MOTION & DESIGN</span>\n          </p>\n        </motion.div>\n\n        {/* CTA Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.2, duration: 0.6 }}\n        >\n          <MagneticButton\n            className=\"group relative px-8 py-4 border border-white/30 text-sm font-medium tracking-widest hover:bg-white hover:text-black transition-all duration-300\"\n            strength={0.3}\n            onClick={() => document.getElementById('work')?.scrollIntoView({ behavior: 'smooth' })}\n          >\n            <span className=\"relative z-10\" data-cursor=\"hover\">VIEW WORK</span>\n            <motion.div\n              className=\"absolute inset-0 bg-white\"\n              initial={{ scaleX: 0 }}\n              whileHover={{ scaleX: 1 }}\n              transition={{ duration: 0.3 }}\n              style={{ originX: 0 }}\n            />\n          </MagneticButton>\n        </motion.div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.5, duration: 0.6 }}\n        >\n          <motion.div\n            className=\"w-6 h-10 border border-white/30 rounded-full flex justify-center\"\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n          >\n            <motion.div\n              className=\"w-1 h-3 bg-white rounded-full mt-2\"\n              animate={{ opacity: [1, 0, 1] }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n            />\n          </motion.div>\n          <p className=\"text-xs text-white/50 mt-2 tracking-widest\">SCROLL</p>\n        </motion.div>\n      </div>\n\n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full opacity-60\"\n        animate={{\n          y: [0, -20, 0],\n          opacity: [0.6, 1, 0.6],\n        }}\n        transition={{\n          duration: 3,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n      <motion.div\n        className=\"absolute top-3/4 right-1/4 w-1 h-1 bg-white rounded-full opacity-40\"\n        animate={{\n          y: [0, -15, 0],\n          opacity: [0.4, 0.8, 0.4],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1,\n        }}\n      />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAI;QAC7B,SAAS,CAAC,IAAc,CAAC;gBACvB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAM;wBAAG;wBAAM;qBAAE;gBAC1B;YACF,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,OAAO;gBACP,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAC1B;QACF;IACF;IAEA,MAAM,aAAa;QAAC;QAAU;QAAW;KAAW;IAEpD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,QAAQ;gCACR,SAAQ;gCACR,SAAQ;gCACR,UAAU;gCACV,YAAY;oCACV,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;0CAEC;+BAXI;;;;;;;;;;kCAiBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,UAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;;gCAA+F;8CAE1G,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;;;;;;kCAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;kCAExC,cAAA,8OAAC,oIAAA,CAAA,UAAc;4BACb,WAAU;4BACV,UAAU;4BACV,SAAS,IAAM,SAAS,cAAc,CAAC,SAAS,eAAe;oCAAE,UAAU;gCAAS;;8CAEpF,8OAAC;oCAAK,WAAU;oCAAgB,eAAY;8CAAQ;;;;;;8CACpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAE;oCACrB,YAAY;wCAAE,QAAQ;oCAAE;oCACxB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,OAAO;wCAAE,SAAS;oCAAE;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;;0CAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAY;0CAE/D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;oCAAC;oCAC9B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;;;;;;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;0BAK9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ProjectGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\n\ninterface Project {\n  id: string;\n  title: string;\n  category: string;\n  year: string;\n  videoUrl: string;\n  description: string;\n  slug: string;\n}\n\nconst projects: Project[] = [\n  {\n    id: '01',\n    title: 'KINETIC TYPOGRAPHY',\n    category: 'MOTION GRAPHICS',\n    year: '2024',\n    videoUrl: '/videos/kinetic-typography.mp4',\n    description: 'Dynamic text animations for brand storytelling',\n    slug: 'kinetic-typography'\n  },\n  {\n    id: '02',\n    title: 'BRAND IDENTITY',\n    category: 'VISUAL DESIGN',\n    year: '2024',\n    videoUrl: '/videos/brand-identity.mp4',\n    description: 'Complete visual identity system with motion elements',\n    slug: 'brand-identity'\n  },\n  {\n    id: '03',\n    title: 'UI ANIMATIONS',\n    category: 'INTERFACE',\n    year: '2023',\n    videoUrl: '/videos/ui-animations.mp4',\n    description: 'Micro-interactions and interface animations',\n    slug: 'ui-animations'\n  },\n  {\n    id: '04',\n    title: 'EXPERIMENTAL',\n    category: 'CREATIVE',\n    year: '2023',\n    videoUrl: '/videos/experimental.mp4',\n    description: 'Abstract motion graphics and visual experiments',\n    slug: 'experimental'\n  },\n  {\n    id: '05',\n    title: 'COMMERCIAL',\n    category: 'ADVERTISING',\n    year: '2024',\n    videoUrl: '/videos/commercial.mp4',\n    description: 'Motion graphics for commercial campaigns',\n    slug: 'commercial'\n  },\n  {\n    id: '06',\n    title: 'TITLE SEQUENCE',\n    category: 'FILM',\n    year: '2023',\n    videoUrl: '/videos/title-sequence.mp4',\n    description: 'Cinematic title sequences and credits',\n    slug: 'title-sequence'\n  }\n];\n\ninterface ProjectItemProps {\n  project: Project;\n  onHover: (project: Project | null) => void;\n  isActive: boolean;\n}\n\nconst ProjectItem = ({ project, onHover, isActive }: ProjectItemProps) => {\n  return (\n    <Link href={`/project/${project.slug}`}>\n      <motion.div\n        className=\"relative cursor-pointer group\"\n        onMouseEnter={() => onHover(project)}\n        onMouseLeave={() => onHover(null)}\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}\n      >\n        <div className=\"border-b border-white/20 py-6 md:py-8 px-4 transition-all duration-300 hover:bg-white/5\" data-cursor=\"hover\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4 md:space-x-8 flex-1\">\n              <span className=\"text-xs md:text-sm font-mono text-white/60 w-6 md:w-8 flex-shrink-0\">{project.id}</span>\n              <div className=\"flex-1 min-w-0\">\n                <h2 className=\"text-xl md:text-3xl lg:text-4xl xl:text-5xl font-black text-brutalist tracking-tight leading-tight\">\n                  {project.title}\n                </h2>\n                <p className=\"text-xs md:text-sm text-white/60 mt-1\">{project.category}</p>\n              </div>\n            </div>\n            <div className=\"text-right flex-shrink-0 ml-4\">\n              <span className=\"text-xs md:text-sm font-mono text-white/60\">{project.year}</span>\n              <motion.div\n                className=\"w-4 h-4 md:w-6 md:h-6 border border-white/40 rounded-full mt-2 ml-auto\"\n                animate={{\n                  scale: isActive ? 1.2 : 1,\n                  backgroundColor: isActive ? 'rgba(255,255,255,0.2)' : 'transparent'\n                }}\n                transition={{ duration: 0.2 }}\n              />\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </Link>\n  );\n};\n\ninterface VideoPreviewProps {\n  project: Project | null;\n}\n\nconst VideoPreview = ({ project }: VideoPreviewProps) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  // Preload video when component mounts\n  useEffect(() => {\n    if (project && videoRef.current) {\n      videoRef.current.load();\n    }\n  }, [project]);\n\n  return (\n    <AnimatePresence>\n      {project && (\n        <motion.div\n          className=\"fixed inset-0 pointer-events-none z-50 flex items-center justify-center\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <motion.div\n            className=\"relative\"\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.8, opacity: 0 }}\n            transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}\n          >\n            <div className=\"relative w-80 md:w-96 h-48 md:h-64 bg-black border border-white/20 overflow-hidden mx-auto\">\n              <div className=\"w-full h-full bg-gradient-to-br from-gray-900 to-black flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 border-2 border-white/20 rounded-full flex items-center justify-center mb-4 mx-auto\">\n                    <div className=\"w-0 h-0 border-l-[8px] border-l-white/60 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1\"></div>\n                  </div>\n                  <p className=\"text-white/60 text-sm font-medium\">{project.category}</p>\n                  <p className=\"text-white/40 text-xs mt-1\">Video Preview</p>\n                </div>\n              </div>\n              <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\">\n                <p className=\"text-sm text-white/80\">{project.description}</p>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function ProjectGrid() {\n  const [hoveredProject, setHoveredProject] = useState<Project | null>(null);\n\n  return (\n    <div className=\"relative\">\n      <div className=\"max-w-7xl mx-auto\">\n        {projects.map((project, index) => (\n          <ProjectItem\n            key={project.id}\n            project={project}\n            onHover={setHoveredProject}\n            isActive={hoveredProject?.id === project.id}\n          />\n        ))}\n      </div>\n      <VideoPreview project={hoveredProject} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAgBA,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;QACb,MAAM;IACR;CACD;AAQD,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAoB;IACnE,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;kBACpC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,cAAc,IAAM,QAAQ;YAC5B,cAAc,IAAM,QAAQ;YAC5B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAAC;sBAEtD,cAAA,8OAAC;gBAAI,WAAU;gBAA0F,eAAY;0BACnH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAuE,QAAQ,EAAE;;;;;;8CACjG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDAAyC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;sCAG1E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA8C,QAAQ,IAAI;;;;;;8CAC1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,OAAO,WAAW,MAAM;wCACxB,iBAAiB,WAAW,0BAA0B;oCACxD;oCACA,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;AAMA,MAAM,eAAe,CAAC,EAAE,OAAO,EAAqB;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,SAAS,OAAO,EAAE;YAC/B,SAAS,OAAO,CAAC,IAAI;QACvB;IACF,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;sBAE5B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAG;wBAAM;qBAAE;gBAAC;0BAEtD,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAE,WAAU;kDAAqC,QAAQ,QAAQ;;;;;;kDAClE,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE;AAEe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wBAEC,SAAS;wBACT,SAAS;wBACT,UAAU,gBAAgB,OAAO,QAAQ,EAAE;uBAHtC,QAAQ,EAAE;;;;;;;;;;0BAOrB,8OAAC;gBAAa,SAAS;;;;;;;;;;;;AAG7B", "debugId": null}}]}