{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_c25d1f70.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_c25d1f70-module__lJUfEG__className\",\n  \"variable\": \"inter_c25d1f70-module__lJUfEG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_c25d1f70.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ScrollAnimations.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Parallax = registerClientReference(\n    function() { throw new Error(\"Attempted to call Parallax() from the server but Parallax is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"Parallax\",\n);\nexport const ProgressBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressBar() from the server but ProgressBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"ProgressBar\",\n);\nexport const ScaleOnScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScaleOnScroll() from the server but ScaleOnScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"ScaleOnScroll\",\n);\nexport const ScrollReveal = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScrollReveal() from the server but ScrollReveal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"ScrollReveal\",\n);\nexport const StaggeredReveal = registerClientReference(\n    function() { throw new Error(\"Attempted to call StaggeredReveal() from the server but StaggeredReveal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx <module evaluation>\",\n    \"StaggeredReveal\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qEACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ScrollAnimations.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Parallax = registerClientReference(\n    function() { throw new Error(\"Attempted to call Parallax() from the server but Parallax is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"Parallax\",\n);\nexport const ProgressBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressBar() from the server but ProgressBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"ProgressBar\",\n);\nexport const ScaleOnScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScaleOnScroll() from the server but ScaleOnScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"ScaleOnScroll\",\n);\nexport const ScrollReveal = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScrollReveal() from the server but ScrollReveal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"ScrollReveal\",\n);\nexport const StaggeredReveal = registerClientReference(\n    function() { throw new Error(\"Attempted to call StaggeredReveal() from the server but StaggeredReveal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollAnimations.tsx\",\n    \"StaggeredReveal\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,iDACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ProgressBar } from \"@/components/ScrollAnimations\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n  weight: [\"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Motion Designer Portfolio\",\n  description: \"A brutalist portfolio showcasing motion graphics and visual design\",\n  keywords: [\"motion graphics\", \"design\", \"portfolio\", \"animation\", \"visual design\"],\n  authors: [{ name: \"Motion Designer\" }],\n};\n\nexport const viewport = {\n  width: 'device-width',\n  initialScale: 1,\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <body className={`${inter.variable} font-sans bg-black text-white antialiased`}>\n        <ProgressBar />\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;;;;;AAQO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAmB;QAAU;QAAa;QAAa;KAAgB;IAClF,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;AACxC;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,0CAA0C,CAAC;;8BAC5E,8OAAC,sIAAA,CAAA,cAAW;;;;;gBACX;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}