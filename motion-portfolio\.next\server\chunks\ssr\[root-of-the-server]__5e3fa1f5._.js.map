{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/CursorFollower.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\n\ninterface CursorState {\n  x: number;\n  y: number;\n  isHovering: boolean;\n  isClicking: boolean;\n}\n\nconst CursorFollower = () => {\n  const [cursor, setCursor] = useState<CursorState>({\n    x: 0,\n    y: 0,\n    isHovering: false,\n    isClicking: false,\n  });\n\n  useEffect(() => {\n    const updateCursor = (e: MouseEvent) => {\n      setCursor(prev => ({\n        ...prev,\n        x: e.clientX,\n        y: e.clientY,\n      }));\n    };\n\n    const handleMouseDown = () => {\n      setCursor(prev => ({ ...prev, isClicking: true }));\n    };\n\n    const handleMouseUp = () => {\n      setCursor(prev => ({ ...prev, isClicking: false }));\n    };\n\n    const handleMouseEnter = (e: Event) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('a, button, [data-cursor=\"hover\"]')) {\n        setCursor(prev => ({ ...prev, isHovering: true }));\n      }\n    };\n\n    const handleMouseLeave = (e: Event) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('a, button, [data-cursor=\"hover\"]')) {\n        setCursor(prev => ({ ...prev, isHovering: false }));\n      }\n    };\n\n    // Add event listeners\n    window.addEventListener('mousemove', updateCursor);\n    window.addEventListener('mousedown', handleMouseDown);\n    window.addEventListener('mouseup', handleMouseUp);\n    \n    // Add hover detection for interactive elements\n    document.addEventListener('mouseenter', handleMouseEnter, true);\n    document.addEventListener('mouseleave', handleMouseLeave, true);\n\n    return () => {\n      window.removeEventListener('mousemove', updateCursor);\n      window.removeEventListener('mousedown', handleMouseDown);\n      window.removeEventListener('mouseup', handleMouseUp);\n      document.removeEventListener('mouseenter', handleMouseEnter, true);\n      document.removeEventListener('mouseleave', handleMouseLeave, true);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-4 h-4 bg-white rounded-full pointer-events-none z-[9999] mix-blend-difference\"\n        animate={{\n          x: cursor.x - 8,\n          y: cursor.y - 8,\n          scale: cursor.isClicking ? 0.8 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 500,\n          damping: 28,\n        }}\n      />\n      \n      {/* Outer cursor ring */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-8 h-8 border border-white/30 rounded-full pointer-events-none z-[9998]\"\n        animate={{\n          x: cursor.x - 16,\n          y: cursor.y - 16,\n          scale: cursor.isHovering ? 1.5 : 1,\n          opacity: cursor.isHovering ? 0.8 : 0.3,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 200,\n          damping: 20,\n        }}\n      />\n\n      {/* Trailing dots */}\n      <motion.div\n        className=\"fixed top-0 left-0 w-2 h-2 bg-white/20 rounded-full pointer-events-none z-[9997]\"\n        animate={{\n          x: cursor.x - 4,\n          y: cursor.y - 4,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 100,\n          damping: 30,\n        }}\n      />\n    </>\n  );\n};\n\nexport default CursorFollower;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,iBAAiB;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAChD,GAAG;QACH,GAAG;QACH,YAAY;QACZ,YAAY;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,GAAG,EAAE,OAAO;oBACZ,GAAG,EAAE,OAAO;gBACd,CAAC;QACH;QAEA,MAAM,kBAAkB;YACtB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAK,CAAC;QAClD;QAEA,MAAM,gBAAgB;YACpB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAM,CAAC;QACnD;QAEA,MAAM,mBAAmB,CAAC;YACxB,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,CAAC,qCAAqC;gBACtD,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAK,CAAC;YAClD;QACF;QAEA,MAAM,mBAAmB,CAAC;YACxB,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,CAAC,qCAAqC;gBACtD,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAM,CAAC;YACnD;QACF;QAEA,sBAAsB;QACtB,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,+CAA+C;QAC/C,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;QAC1D,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;QAE1D,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,WAAW;YACtC,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;YAC7D,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;QAC/D;IACF,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,OAAO,CAAC,GAAG;oBACd,GAAG,OAAO,CAAC,GAAG;oBACd,OAAO,OAAO,UAAU,GAAG,MAAM;gBACnC;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,OAAO,CAAC,GAAG;oBACd,GAAG,OAAO,CAAC,GAAG;oBACd,OAAO,OAAO,UAAU,GAAG,MAAM;oBACjC,SAAS,OAAO,UAAU,GAAG,MAAM;gBACrC;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,OAAO,CAAC,GAAG;oBACd,GAAG,OAAO,CAAC,GAAG;gBAChB;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform, useSpring } from 'framer-motion';\nimport { useRef, ReactNode } from 'react';\n\ninterface ScrollRevealProps {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n}\n\nexport const ScrollReveal = ({ children, className = '', delay = 0 }: ScrollRevealProps) => {\n  const ref = useRef(null);\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      viewport={{ once: true, margin: \"-100px\" }}\n      transition={{\n        duration: 0.8,\n        delay: delay,\n        ease: [0.22, 1, 0.36, 1],\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\ninterface ParallaxProps {\n  children: ReactNode;\n  offset?: number;\n  className?: string;\n}\n\nexport const Parallax = ({ children, offset = 50, className = '' }: ParallaxProps) => {\n  const ref = useRef(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [0, offset]);\n  const smoothY = useSpring(y, { stiffness: 100, damping: 30 });\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      style={{ y: smoothY }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\ninterface ScaleOnScrollProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport const ScaleOnScroll = ({ children, className = '' }: ScaleOnScrollProps) => {\n  const ref = useRef(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 1.2]);\n  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      style={{ scale, opacity }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport const ProgressBar = () => {\n  const { scrollYProgress } = useScroll();\n  const scaleX = useSpring(scrollYProgress, {\n    stiffness: 100,\n    damping: 30,\n    restDelta: 0.001\n  });\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 right-0 h-1 bg-white z-50 origin-left\"\n      style={{ scaleX }}\n    />\n  );\n};\n\ninterface StaggeredRevealProps {\n  children: ReactNode[];\n  className?: string;\n  stagger?: number;\n}\n\nexport const StaggeredReveal = ({ children, className = '', stagger = 0.1 }: StaggeredRevealProps) => {\n  const container = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: stagger,\n      },\n    },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.22, 1, 0.36, 1],\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      className={className}\n      variants={container}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-50px\" }}\n    >\n      {children.map((child, index) => (\n        <motion.div key={index} variants={item}>\n          {child}\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AAAA;AACA;AAHA;;;;AAWO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,CAAC,EAAqB;IACrF,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEnB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;QAC1B;kBAEC;;;;;;AAGP;AAQO,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,EAAiB;IAC/E,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG;KAAO;IAC3D,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QAAE,WAAW;QAAK,SAAS;IAAG;IAE3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,OAAO;YAAE,GAAG;QAAQ;kBAEnB;;;;;;AAGP;AAOO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAsB;IAC5E,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;KAAE,EAAE;QAAC;QAAK;QAAG;KAAI;IACtE,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,OAAO;YAAE;YAAO;QAAQ;kBAEvB;;;;;;AAGP;AAEO,MAAM,cAAc;IACzB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QACxC,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YAAE;QAAO;;;;;;AAGtB;AAQO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,UAAU,GAAG,EAAwB;IAC/F,MAAM,YAAY;QAChB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,OAAO;QACX,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAC1B;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;kBAEvC,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAa,UAAU;0BAC/B;eADc;;;;;;;;;;AAMzB", "debugId": null}}]}