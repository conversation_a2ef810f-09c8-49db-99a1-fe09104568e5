{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ProjectGrid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProjectGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProjectGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ProjectGrid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProjectGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProjectGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/MagneticButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MagneticButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MagneticButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/MagneticButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MagneticButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MagneticButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/AnimatedText.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedText.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedText.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/AnimatedText.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AnimatedText.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AnimatedText.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/app/page.tsx"], "sourcesContent": ["import Navigation from '@/components/Navigation';\nimport Hero from '@/components/Hero';\nimport ProjectGrid from '@/components/ProjectGrid';\nimport { ScrollReveal, StaggeredReveal } from '@/components/ScrollAnimations';\nimport MagneticButton from '@/components/MagneticButton';\nimport AnimatedText from '@/components/AnimatedText';\n\nexport default function Home() {\n  return (\n    <main className=\"bg-black text-white min-h-screen\">\n      <Navigation />\n      <Hero />\n\n      {/* Work Section */}\n      <section id=\"work\" className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <ScrollReveal className=\"mb-16\">\n            <AnimatedText className=\"text-4xl md:text-6xl font-black text-brutalist mb-4\">\n              SELECTED WORK\n            </AnimatedText>\n            <p className=\"text-white/60 text-lg max-w-2xl\">\n              A collection of motion graphics, visual design, and creative experiments\n              that push the boundaries of digital storytelling.\n            </p>\n          </ScrollReveal>\n          <ScrollReveal delay={0.2}>\n            <ProjectGrid />\n          </ScrollReveal>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-20 bg-white/5\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 gap-16 items-center\">\n            <ScrollReveal>\n              <AnimatedText className=\"text-4xl md:text-6xl font-black text-brutalist mb-8\">\n                ABOUT\n              </AnimatedText>\n              <div className=\"space-y-6 text-lg text-white/80\">\n                <ScrollReveal delay={0.2}>\n                  <p>\n                    I'm a motion graphic designer passionate about creating visual narratives\n                    that captivate and inspire. With expertise in animation, typography, and\n                    visual storytelling, I bring ideas to life through motion.\n                  </p>\n                </ScrollReveal>\n                <ScrollReveal delay={0.4}>\n                  <p>\n                    My work spans across commercial advertising, film title sequences,\n                    brand identity systems, and experimental creative projects. I believe\n                    in the power of motion to communicate complex ideas simply and beautifully.\n                  </p>\n                </ScrollReveal>\n              </div>\n            </ScrollReveal>\n            <ScrollReveal delay={0.3}>\n              <div className=\"space-y-8\">\n                <div>\n                  <h3 className=\"text-xl font-bold mb-4\">EXPERTISE</h3>\n                  <StaggeredReveal className=\"grid grid-cols-2 gap-4 text-sm\">\n                    {['Motion Graphics', 'Visual Design', 'Typography', 'Brand Identity', 'UI Animation', 'Creative Direction'].map((skill, index) => (\n                      <div key={index}>{skill}</div>\n                    ))}\n                  </StaggeredReveal>\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold mb-4\">TOOLS</h3>\n                  <StaggeredReveal className=\"grid grid-cols-2 gap-4 text-sm text-white/60\">\n                    {['After Effects', 'Cinema 4D', 'Figma', 'Illustrator', 'Photoshop', 'Premiere Pro'].map((tool, index) => (\n                      <div key={index}>{tool}</div>\n                    ))}\n                  </StaggeredReveal>\n                </div>\n              </div>\n            </ScrollReveal>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <ScrollReveal>\n            <AnimatedText className=\"text-4xl md:text-6xl font-black text-brutalist mb-8\">\n              LET'S CREATE\n            </AnimatedText>\n          </ScrollReveal>\n          <ScrollReveal delay={0.2}>\n            <p className=\"text-xl text-white/80 mb-12 max-w-2xl mx-auto\">\n              Ready to bring your vision to life? Let's collaborate on something extraordinary.\n            </p>\n          </ScrollReveal>\n          <ScrollReveal delay={0.4}>\n            <div className=\"flex flex-col sm:flex-row gap-6 justify-center\">\n              <MagneticButton\n                className=\"px-8 py-4 border border-white/30 text-sm font-medium tracking-widest hover:bg-white hover:text-black transition-all duration-300\"\n                strength={0.2}\n              >\n                <a href=\"mailto:<EMAIL>\" data-cursor=\"hover\">\n                  GET IN TOUCH\n                </a>\n              </MagneticButton>\n              <MagneticButton\n                className=\"px-8 py-4 bg-white text-black text-sm font-medium tracking-widest hover:bg-white/90 transition-all duration-300\"\n                strength={0.2}\n              >\n                <a href=\"#\" data-cursor=\"hover\">\n                  VIEW RESUME\n                </a>\n              </MagneticButton>\n            </div>\n          </ScrollReveal>\n        </div>\n      </section>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,gIAAA,CAAA,UAAU;;;;;0BACX,8OAAC,0HAAA,CAAA,UAAI;;;;;0BAGL,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,UAAY;oCAAC,WAAU;8CAAsD;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC,sIAAA,CAAA,eAAY;4BAAC,OAAO;sCACnB,cAAA,8OAAC,iIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,UAAY;wCAAC,WAAU;kDAAsD;;;;;;kDAG9E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,eAAY;gDAAC,OAAO;0DACnB,cAAA,8OAAC;8DAAE;;;;;;;;;;;0DAML,8OAAC,sIAAA,CAAA,eAAY;gDAAC,OAAO;0DACnB,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;0CAQT,8OAAC,sIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC,sIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB;wDAAC;wDAAmB;wDAAiB;wDAAc;wDAAkB;wDAAgB;qDAAqB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtH,8OAAC;sEAAiB;2DAAR;;;;;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC,sIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB;wDAAC;wDAAiB;wDAAa;wDAAS;wDAAe;wDAAa;qDAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9F,8OAAC;sEAAiB;2DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW1B,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,UAAY;gCAAC,WAAU;0CAAsD;;;;;;;;;;;sCAIhF,8OAAC,sIAAA,CAAA,eAAY;4BAAC,OAAO;sCACnB,cAAA,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;sCAI/D,8OAAC,sIAAA,CAAA,eAAY;4BAAC,OAAO;sCACnB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,UAAc;wCACb,WAAU;wCACV,UAAU;kDAEV,cAAA,8OAAC;4CAAE,MAAK;4CAAkC,eAAY;sDAAQ;;;;;;;;;;;kDAIhE,8OAAC,oIAAA,CAAA,UAAc;wCACb,WAAU;wCACV,UAAU;kDAEV,cAAA,8OAAC;4CAAE,MAAK;4CAAI,eAAY;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}]}