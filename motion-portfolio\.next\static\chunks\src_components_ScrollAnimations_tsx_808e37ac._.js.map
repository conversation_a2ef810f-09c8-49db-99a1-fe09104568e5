{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coder/Portfolio%20Claude/motion-portfolio/src/components/ScrollAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform, useSpring } from 'framer-motion';\nimport { useRef, ReactNode } from 'react';\n\ninterface ScrollRevealProps {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n}\n\nexport const ScrollReveal = ({ children, className = '', delay = 0 }: ScrollRevealProps) => {\n  const ref = useRef(null);\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      viewport={{ once: true, margin: \"-100px\" }}\n      transition={{\n        duration: 0.8,\n        delay: delay,\n        ease: [0.22, 1, 0.36, 1],\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\ninterface ParallaxProps {\n  children: ReactNode;\n  offset?: number;\n  className?: string;\n}\n\nexport const Parallax = ({ children, offset = 50, className = '' }: ParallaxProps) => {\n  const ref = useRef(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [0, offset]);\n  const smoothY = useSpring(y, { stiffness: 100, damping: 30 });\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      style={{ y: smoothY }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\ninterface ScaleOnScrollProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport const ScaleOnScroll = ({ children, className = '' }: ScaleOnScrollProps) => {\n  const ref = useRef(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 1.2]);\n  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      style={{ scale, opacity }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport const ProgressBar = () => {\n  const { scrollYProgress } = useScroll();\n  const scaleX = useSpring(scrollYProgress, {\n    stiffness: 100,\n    damping: 30,\n    restDelta: 0.001\n  });\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 right-0 h-1 bg-white z-50 origin-left\"\n      style={{ scaleX }}\n    />\n  );\n};\n\ninterface StaggeredRevealProps {\n  children: ReactNode[];\n  className?: string;\n  stagger?: number;\n}\n\nexport const StaggeredReveal = ({ children, className = '', stagger = 0.1 }: StaggeredRevealProps) => {\n  const container = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: stagger,\n      },\n    },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.22, 1, 0.36, 1],\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      className={className}\n      variants={container}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-50px\" }}\n    >\n      {children.map((child, index) => (\n        <motion.div key={index} variants={item}>\n          {child}\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAWO,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,CAAC,EAAqB;;IACrF,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;QAC1B;kBAEC;;;;;;AAGP;GAnBa;KAAA;AA2BN,MAAM,WAAW;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,EAAiB;;IAC/E,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG;KAAO;IAC3D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QAAE,WAAW;QAAK,SAAS;IAAG;IAE3D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,OAAO;YAAE,GAAG;QAAQ;kBAEnB;;;;;;AAGP;IAnBa;;QAEiB,4KAAA,CAAA,YAAS;QAK3B,+KAAA,CAAA,eAAY;QACN,4KAAA,CAAA,YAAS;;;MARd;AA0BN,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAsB;;IAC5E,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,QAAQ,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;KAAE,EAAE;QAAC;QAAK;QAAG;KAAI;IACtE,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,OAAO;YAAE;YAAO;QAAQ;kBAEvB;;;;;;AAGP;IAnBa;;QAEiB,4KAAA,CAAA,YAAS;QAKvB,+KAAA,CAAA,eAAY;QACV,+KAAA,CAAA,eAAY;;;MARjB;AAqBN,MAAM,cAAc;;IACzB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QACxC,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YAAE;QAAO;;;;;;AAGtB;IAda;;QACiB,4KAAA,CAAA,YAAS;QACtB,4KAAA,CAAA,YAAS;;;MAFb;AAsBN,MAAM,kBAAkB;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,UAAU,GAAG,EAAwB;IAC/F,MAAM,YAAY;QAChB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,OAAO;QACX,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAC1B;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;kBAEvC,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAa,UAAU;0BAC/B;eADc;;;;;;;;;;AAMzB;MAtCa", "debugId": null}}]}