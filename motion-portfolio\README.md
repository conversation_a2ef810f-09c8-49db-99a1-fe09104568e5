# Motion Designer Portfolio

A brutalist, minimalist portfolio website for motion graphic designers built with Next.js, TypeScript, Tailwind CSS, and Framer Motion.

## Features

- **Brutalist Design**: Bold typography, dark theme, and minimalist aesthetic
- **Interactive Animations**: Smooth page transitions, hover effects, and micro-interactions
- **Video Previews**: Hover over project titles to see video previews
- **Responsive Design**: Optimized for all screen sizes
- **Performance Optimized**: Fast loading with modern web technologies
- **Custom Cursor**: Enhanced cursor interactions
- **Magnetic Buttons**: Interactive button animations
- **Scroll Animations**: Elements animate as they come into view

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Font**: Inter (Google Fonts)
- **Icons**: Lucide React

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd motion-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Add video content**
   - Place your video files in `public/videos/`
   - Required videos:
     - `kinetic-typography.mp4`
     - `brand-identity.mp4`
     - `ui-animations.mp4`
     - `experimental.mp4`
     - `commercial.mp4`
     - `title-sequence.mp4`

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and design system
│   ├── layout.tsx           # Root layout with navigation
│   ├── page.tsx             # Homepage
│   └── project/[slug]/      # Individual project pages
├── components/
│   ├── AnimatedText.tsx     # Text animation component
│   ├── CursorFollower.tsx   # Custom cursor component
│   ├── Hero.tsx             # Homepage hero section
│   ├── Loading.tsx          # Loading screen component
│   ├── MagneticButton.tsx   # Interactive button component
│   ├── Navigation.tsx       # Site navigation
│   ├── PageTransition.tsx   # Page transition wrapper
│   ├── ProjectGrid.tsx      # Project listing grid
│   └── ScrollAnimations.tsx # Scroll-based animations
public/
├── videos/                  # Video assets
└── images/                  # Image assets
```

## Customization

### Design System
The design system is defined in `src/app/globals.css` with custom CSS variables:
- Colors: Black background, white text, custom accent colors
- Typography: Inter font with brutalist styling
- Spacing: Custom spacing scale
- Animations: Smooth transitions and easing

### Adding Projects
1. Update the projects array in `src/components/ProjectGrid.tsx`
2. Add corresponding project data in `src/app/project/[slug]/page.tsx`
3. Add video files to `public/videos/`

### Animations
All animations use Framer Motion for smooth, performant motion:
- Page transitions
- Scroll-triggered animations
- Hover effects
- Micro-interactions

## Performance

- **Optimized Images**: Next.js Image component for automatic optimization
- **Lazy Loading**: Videos and images load on demand
- **Code Splitting**: Automatic code splitting with Next.js
- **Modern CSS**: Tailwind CSS v4 for minimal bundle size

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Deployment

### Vercel (Recommended)
```bash
npm run build
vercel deploy
```

### Other Platforms
```bash
npm run build
npm start
```

## License

MIT License - feel free to use this template for your own portfolio.

## Credits

Built with modern web technologies and design principles for motion graphic designers who want to showcase their work with style and performance.
