@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
  --accent: #ffffff;
  --muted: #333333;
  --border: #222222;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Custom spacing for brutalist design */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 4rem;
  --spacing-2xl: 8rem;

  /* Typography scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;
  --text-8xl: 6rem;
  --text-9xl: 8rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Brutalist typography styles */
.text-brutalist {
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 0.9;
}

.text-brutalist-lg {
  font-weight: 800;
  letter-spacing: -0.01em;
  line-height: 1.1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--foreground);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted);
}

/* Selection styles */
::selection {
  background: var(--foreground);
  color: var(--background);
}
