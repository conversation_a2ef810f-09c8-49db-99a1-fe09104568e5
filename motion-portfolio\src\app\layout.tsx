import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import CursorFollower from "@/components/CursorFollower";
import { ProgressBar } from "@/components/ScrollAnimations";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Motion Designer Portfolio",
  description: "A brutalist portfolio showcasing motion graphics and visual design",
  keywords: ["motion graphics", "design", "portfolio", "animation", "visual design"],
  authors: [{ name: "Motion Designer" }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} font-sans bg-black text-white antialiased cursor-none`}>
        <ProgressBar />
        <CursorFollower />
        {children}
      </body>
    </html>
  );
}
