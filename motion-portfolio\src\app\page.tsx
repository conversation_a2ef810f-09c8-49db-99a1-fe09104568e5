import Navigation from '@/components/Navigation';
import Hero from '@/components/Hero';
import ProjectGrid from '@/components/ProjectGrid';
import { ScrollReveal } from '@/components/ScrollAnimations';
import MagneticButton from '@/components/MagneticButton';

export default function Home() {
  return (
    <main className="bg-black text-white min-h-screen">
      <Navigation />
      <Hero />

      {/* Work Section */}
      <section id="work" className="py-20 mt-20">
        <div className="max-w-7xl mx-auto px-4">
          <ScrollReveal className="mb-16">
            <h2 className="text-4xl md:text-6xl font-black text-brutalist mb-4 leading-tight">
              SELECTED WORK
            </h2>
            <p className="text-white/60 text-lg max-w-2xl leading-relaxed">
              A collection of motion graphics, visual design, and creative experiments
              that push the boundaries of digital storytelling.
            </p>
          </ScrollReveal>
          <ScrollReveal delay={0.2}>
            <ProjectGrid />
          </ScrollReveal>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-16 items-start">
            <ScrollReveal>
              <h2 className="text-4xl md:text-6xl font-black text-brutalist mb-8 leading-tight">
                ABOUT
              </h2>
              <div className="space-y-6 text-lg text-white/80 leading-relaxed">
                <ScrollReveal delay={0.2}>
                  <p>
                    I'm a motion graphic designer passionate about creating visual narratives
                    that captivate and inspire. With expertise in animation, typography, and
                    visual storytelling, I bring ideas to life through motion.
                  </p>
                </ScrollReveal>
                <ScrollReveal delay={0.4}>
                  <p>
                    My work spans across commercial advertising, film title sequences,
                    brand identity systems, and experimental creative projects. I believe
                    in the power of motion to communicate complex ideas simply and beautifully.
                  </p>
                </ScrollReveal>
              </div>
            </ScrollReveal>
            <ScrollReveal delay={0.3}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-bold mb-4 text-white">EXPERTISE</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm text-white/80">
                    {['Motion Graphics', 'Visual Design', 'Typography', 'Brand Identity', 'UI Animation', 'Creative Direction'].map((skill, index) => (
                      <div key={index} className="py-1">{skill}</div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-4 text-white">TOOLS</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm text-white/60">
                    {['After Effects', 'Cinema 4D', 'Figma', 'Illustrator', 'Photoshop', 'Premiere Pro'].map((tool, index) => (
                      <div key={index} className="py-1">{tool}</div>
                    ))}
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <ScrollReveal>
            <h2 className="text-4xl md:text-6xl font-black text-brutalist mb-8 leading-tight">
              LET'S CREATE
            </h2>
          </ScrollReveal>
          <ScrollReveal delay={0.2}>
            <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto leading-relaxed">
              Ready to bring your vision to life? Let's collaborate on something extraordinary.
            </p>
          </ScrollReveal>
          <ScrollReveal delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <MagneticButton
                className="px-8 py-4 border border-white/30 text-sm font-medium tracking-widest hover:bg-white hover:text-black transition-all duration-300"
                strength={0.2}
              >
                <span data-cursor="hover">GET IN TOUCH</span>
              </MagneticButton>
              <MagneticButton
                className="px-8 py-4 bg-white text-black text-sm font-medium tracking-widest hover:bg-white/90 transition-all duration-300"
                strength={0.2}
              >
                <span data-cursor="hover">VIEW RESUME</span>
              </MagneticButton>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </main>
  );
}
