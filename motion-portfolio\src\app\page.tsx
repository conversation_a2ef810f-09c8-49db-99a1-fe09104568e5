import Navigation from '@/components/Navigation';
import Hero from '@/components/Hero';
import ProjectGrid from '@/components/ProjectGrid';
import { ScrollReveal, StaggeredReveal } from '@/components/ScrollAnimations';
import MagneticButton from '@/components/MagneticButton';
import AnimatedText from '@/components/AnimatedText';

export default function Home() {
  return (
    <main className="bg-black text-white min-h-screen">
      <Navigation />
      <Hero />

      {/* Work Section */}
      <section id="work" className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <ScrollReveal className="mb-16">
            <AnimatedText className="text-4xl md:text-6xl font-black text-brutalist mb-4">
              SELECTED WORK
            </AnimatedText>
            <p className="text-white/60 text-lg max-w-2xl">
              A collection of motion graphics, visual design, and creative experiments
              that push the boundaries of digital storytelling.
            </p>
          </ScrollReveal>
          <ScrollReveal delay={0.2}>
            <ProjectGrid />
          </ScrollReveal>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <ScrollReveal>
              <AnimatedText className="text-4xl md:text-6xl font-black text-brutalist mb-8">
                ABOUT
              </AnimatedText>
              <div className="space-y-6 text-lg text-white/80">
                <ScrollReveal delay={0.2}>
                  <p>
                    I'm a motion graphic designer passionate about creating visual narratives
                    that captivate and inspire. With expertise in animation, typography, and
                    visual storytelling, I bring ideas to life through motion.
                  </p>
                </ScrollReveal>
                <ScrollReveal delay={0.4}>
                  <p>
                    My work spans across commercial advertising, film title sequences,
                    brand identity systems, and experimental creative projects. I believe
                    in the power of motion to communicate complex ideas simply and beautifully.
                  </p>
                </ScrollReveal>
              </div>
            </ScrollReveal>
            <ScrollReveal delay={0.3}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-bold mb-4">EXPERTISE</h3>
                  <StaggeredReveal className="grid grid-cols-2 gap-4 text-sm">
                    {['Motion Graphics', 'Visual Design', 'Typography', 'Brand Identity', 'UI Animation', 'Creative Direction'].map((skill, index) => (
                      <div key={index}>{skill}</div>
                    ))}
                  </StaggeredReveal>
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-4">TOOLS</h3>
                  <StaggeredReveal className="grid grid-cols-2 gap-4 text-sm text-white/60">
                    {['After Effects', 'Cinema 4D', 'Figma', 'Illustrator', 'Photoshop', 'Premiere Pro'].map((tool, index) => (
                      <div key={index}>{tool}</div>
                    ))}
                  </StaggeredReveal>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <ScrollReveal>
            <AnimatedText className="text-4xl md:text-6xl font-black text-brutalist mb-8">
              LET'S CREATE
            </AnimatedText>
          </ScrollReveal>
          <ScrollReveal delay={0.2}>
            <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto">
              Ready to bring your vision to life? Let's collaborate on something extraordinary.
            </p>
          </ScrollReveal>
          <ScrollReveal delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <MagneticButton
                className="px-8 py-4 border border-white/30 text-sm font-medium tracking-widest hover:bg-white hover:text-black transition-all duration-300"
                strength={0.2}
              >
                <a href="mailto:<EMAIL>" data-cursor="hover">
                  GET IN TOUCH
                </a>
              </MagneticButton>
              <MagneticButton
                className="px-8 py-4 bg-white text-black text-sm font-medium tracking-widest hover:bg-white/90 transition-all duration-300"
                strength={0.2}
              >
                <a href="#" data-cursor="hover">
                  VIEW RESUME
                </a>
              </MagneticButton>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </main>
  );
}
