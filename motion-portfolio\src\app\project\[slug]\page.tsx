import { notFound } from 'next/navigation';
import { motion } from 'framer-motion';
import Navigation from '@/components/Navigation';
import { ScrollReveal } from '@/components/ScrollAnimations';
import MagneticButton from '@/components/MagneticButton';
import LazyVideo from '@/components/LazyVideo';

interface Project {
  id: string;
  title: string;
  category: string;
  year: string;
  videoUrl: string;
  description: string;
  longDescription: string;
  client: string;
  role: string;
  tools: string[];
  images: string[];
}

const projects: Record<string, Project> = {
  'kinetic-typography': {
    id: '01',
    title: 'KINETIC TYPOGRAPHY',
    category: 'MOTION GRAPHICS',
    year: '2024',
    videoUrl: '/videos/kinetic-typography.mp4',
    description: 'Dynamic text animations for brand storytelling',
    longDescription: 'A comprehensive exploration of kinetic typography, combining bold letterforms with fluid motion to create compelling visual narratives. This project demonstrates the power of animated text to convey emotion, rhythm, and meaning beyond traditional static design.',
    client: 'Creative Agency',
    role: 'Motion Designer',
    tools: ['After Effects', 'Illustrator', 'Cinema 4D'],
    images: ['/images/kinetic-1.jpg', '/images/kinetic-2.jpg', '/images/kinetic-3.jpg']
  },
  'brand-identity': {
    id: '02',
    title: 'BRAND IDENTITY',
    category: 'VISUAL DESIGN',
    year: '2024',
    videoUrl: '/videos/brand-identity.mp4',
    description: 'Complete visual identity system with motion elements',
    longDescription: 'A holistic brand identity system that seamlessly integrates static and motion elements. From logo animations to comprehensive brand guidelines, this project showcases how motion can enhance brand recognition and create memorable experiences.',
    client: 'Tech Startup',
    role: 'Creative Director',
    tools: ['After Effects', 'Figma', 'Illustrator'],
    images: ['/images/brand-1.jpg', '/images/brand-2.jpg', '/images/brand-3.jpg']
  },
  'ui-animations': {
    id: '03',
    title: 'UI ANIMATIONS',
    category: 'INTERFACE',
    year: '2023',
    videoUrl: '/videos/ui-animations.mp4',
    description: 'Micro-interactions and interface animations',
    longDescription: 'Thoughtfully crafted micro-interactions that enhance user experience through subtle motion. These animations provide feedback, guide user attention, and create delightful moments throughout the digital interface.',
    client: 'Mobile App',
    role: 'UI/UX Designer',
    tools: ['Figma', 'Principle', 'After Effects'],
    images: ['/images/ui-1.jpg', '/images/ui-2.jpg', '/images/ui-3.jpg']
  }
};

interface ProjectPageProps {
  params: {
    slug: string;
  };
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const project = projects[params.slug];

  if (!project) {
    notFound();
  }

  return (
    <main className="bg-black text-white min-h-screen">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20">
        <div className="max-w-7xl mx-auto px-4">
          <ScrollReveal>
            <div className="mb-8">
              <span className="text-sm font-mono text-white/60">{project.id}</span>
              <h1 className="text-4xl md:text-6xl lg:text-8xl font-black text-brutalist leading-none tracking-tighter mt-2">
                {project.title}
              </h1>
              <p className="text-lg text-white/60 mt-4">{project.category} • {project.year}</p>
            </div>
          </ScrollReveal>
          
          <ScrollReveal delay={0.2}>
            <div className="aspect-video bg-black border border-white/20 overflow-hidden mb-12">
              <LazyVideo
                src={project.videoUrl}
                autoPlay
                muted
                loop
                playsInline
                className="w-full h-full"
              />
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* Project Details */}
      <section className="py-20 bg-white/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-16">
            <ScrollReveal className="md:col-span-2">
              <h2 className="text-2xl font-bold mb-6">PROJECT OVERVIEW</h2>
              <p className="text-lg text-white/80 leading-relaxed mb-8">
                {project.longDescription}
              </p>
              <p className="text-white/60">
                {project.description}
              </p>
            </ScrollReveal>
            
            <ScrollReveal delay={0.2}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-sm font-bold text-white/60 mb-2">CLIENT</h3>
                  <p className="text-lg">{project.client}</p>
                </div>
                <div>
                  <h3 className="text-sm font-bold text-white/60 mb-2">ROLE</h3>
                  <p className="text-lg">{project.role}</p>
                </div>
                <div>
                  <h3 className="text-sm font-bold text-white/60 mb-2">YEAR</h3>
                  <p className="text-lg">{project.year}</p>
                </div>
                <div>
                  <h3 className="text-sm font-bold text-white/60 mb-2">TOOLS</h3>
                  <div className="space-y-1">
                    {project.tools.map((tool, index) => (
                      <p key={index} className="text-sm text-white/80">{tool}</p>
                    ))}
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <ScrollReveal>
            <div className="flex justify-between items-center">
              <MagneticButton 
                className="px-6 py-3 border border-white/30 text-sm font-medium tracking-widest hover:bg-white hover:text-black transition-all duration-300"
                onClick={() => window.history.back()}
              >
                ← BACK TO WORK
              </MagneticButton>
              
              <MagneticButton 
                className="px-6 py-3 bg-white text-black text-sm font-medium tracking-widest hover:bg-white/90 transition-all duration-300"
                onClick={() => window.location.href = '#contact'}
              >
                NEXT PROJECT →
              </MagneticButton>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </main>
  );
}

export async function generateStaticParams() {
  return Object.keys(projects).map((slug) => ({
    slug,
  }));
}
