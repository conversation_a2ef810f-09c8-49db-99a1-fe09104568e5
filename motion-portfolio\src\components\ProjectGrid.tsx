'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';

interface Project {
  id: string;
  title: string;
  category: string;
  year: string;
  videoUrl: string;
  description: string;
  slug: string;
}

const projects: Project[] = [
  {
    id: '01',
    title: 'KINETIC TYPOGRAPHY',
    category: 'MOTION GRAPHICS',
    year: '2024',
    videoUrl: '/videos/kinetic-typography.mp4',
    description: 'Dynamic text animations for brand storytelling',
    slug: 'kinetic-typography'
  },
  {
    id: '02',
    title: 'BRAND IDENTITY',
    category: 'VISUAL DESIGN',
    year: '2024',
    videoUrl: '/videos/brand-identity.mp4',
    description: 'Complete visual identity system with motion elements',
    slug: 'brand-identity'
  },
  {
    id: '03',
    title: 'UI ANIMATIONS',
    category: 'INTERFACE',
    year: '2023',
    videoUrl: '/videos/ui-animations.mp4',
    description: 'Micro-interactions and interface animations',
    slug: 'ui-animations'
  },
  {
    id: '04',
    title: 'EXPERIMENTAL',
    category: 'CREATIVE',
    year: '2023',
    videoUrl: '/videos/experimental.mp4',
    description: 'Abstract motion graphics and visual experiments',
    slug: 'experimental'
  },
  {
    id: '05',
    title: 'COMMERCIAL',
    category: 'ADVERTISING',
    year: '2024',
    videoUrl: '/videos/commercial.mp4',
    description: 'Motion graphics for commercial campaigns',
    slug: 'commercial'
  },
  {
    id: '06',
    title: 'TITLE SEQUENCE',
    category: 'FILM',
    year: '2023',
    videoUrl: '/videos/title-sequence.mp4',
    description: 'Cinematic title sequences and credits',
    slug: 'title-sequence'
  }
];

interface ProjectItemProps {
  project: Project;
  onHover: (project: Project | null) => void;
  isActive: boolean;
}

const ProjectItem = ({ project, onHover, isActive }: ProjectItemProps) => {
  return (
    <Link href={`/project/${project.slug}`}>
      <motion.div
        className="relative cursor-pointer group"
        onMouseEnter={() => onHover(project)}
        onMouseLeave={() => onHover(null)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
      >
        <div className="border-b border-white/20 py-6 md:py-8 px-4 transition-all duration-300 hover:bg-white/5" data-cursor="hover">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 md:space-x-8 flex-1">
              <span className="text-xs md:text-sm font-mono text-white/60 w-6 md:w-8 flex-shrink-0">{project.id}</span>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl md:text-3xl lg:text-4xl xl:text-5xl font-black text-brutalist tracking-tight leading-tight">
                  {project.title}
                </h2>
                <p className="text-xs md:text-sm text-white/60 mt-1">{project.category}</p>
              </div>
            </div>
            <div className="text-right flex-shrink-0 ml-4">
              <span className="text-xs md:text-sm font-mono text-white/60">{project.year}</span>
              <motion.div
                className="w-4 h-4 md:w-6 md:h-6 border border-white/40 rounded-full mt-2 ml-auto"
                animate={{
                  scale: isActive ? 1.2 : 1,
                  backgroundColor: isActive ? 'rgba(255,255,255,0.2)' : 'transparent'
                }}
                transition={{ duration: 0.2 }}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </Link>
  );
};

interface VideoPreviewProps {
  project: Project | null;
}

const VideoPreview = ({ project }: VideoPreviewProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  // Preload video when component mounts
  useEffect(() => {
    if (project && videoRef.current) {
      videoRef.current.load();
    }
  }, [project]);

  return (
    <AnimatePresence>
      {project && (
        <motion.div
          className="fixed inset-0 pointer-events-none z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
          >
            <div className="relative w-80 md:w-96 h-48 md:h-64 bg-black border border-white/20 overflow-hidden">
              <div className="w-full h-full bg-gradient-to-br from-gray-900 to-black flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 border-2 border-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <div className="w-0 h-0 border-l-[8px] border-l-white/60 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                  </div>
                  <p className="text-white/60 text-sm font-medium">{project.category}</p>
                  <p className="text-white/40 text-xs mt-1">Video Preview</p>
                </div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                <p className="text-sm text-white/80">{project.description}</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default function ProjectGrid() {
  const [hoveredProject, setHoveredProject] = useState<Project | null>(null);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto">
        {projects.map((project, index) => (
          <ProjectItem
            key={project.id}
            project={project}
            onHover={setHoveredProject}
            isActive={hoveredProject?.id === project.id}
          />
        ))}
      </div>
      <VideoPreview project={hoveredProject} />
    </div>
  );
}
